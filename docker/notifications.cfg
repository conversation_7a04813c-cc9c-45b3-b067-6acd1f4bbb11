[main]
sentry_dsn = BOL_SENTRY_DSN
frontend_url = BOL_FRONTEND_URL
service_provider = BOL_SERVICE_PROVIDER
person_org_contract_type = BOL_PERSON_ORG_CONTRACT_TYPE
org_contract_type = BOL_ORG_CONTRACT_TYPE
default_storage = BOL_DEFAULT_STORAGE

[gluu]
base_url = BOL_GLUU_ADDRESS

[celery]
broker_url = CELERY_BROKER_URL
task_default_queue = CELERY_TASK_DEFAULT_QUEUE
task_default_exchange = CELERY_TASK_DEFAULT_EXCHANGE

[bol-data-api]
base_url = BOL_BDA_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
scope =
    uapi_reports_search_id_get,

    uapi_ext_bol_project_list_get,
    uapi_ext_bol_project_suppliers_get,
    uapi_ext_bol_company_list_get,

    bda_project_users_get,
    bda_project_users_search,

    uapi_data_cache_get,
    uapi_data_cache_id_delete,
    uapi_data_cache_id_get,
    uapi_data_cache_id_put,
    uapi_data_cache_post,
    uapi_data_cache_search_id_get,

    bda_creditsafe_account_get,
    bda_creditsafe_account_post,
    bda_creditsafe_account_put,
    bda_creditsafe_account_delete,
    bda_creditsafe_account_search,

[qvarn]
verify_requests = BOL_VERIFY_REQUESTS
base_url = BOL_QVARN_ADDRESS
url_for_orgs = BOL_QVARNLIKE_URL_FOR_ORGS
url_for_cards = BOL_QVARNLIKE_URL_FOR_CARDS
url_for_persons = BOL_QVARNLIKE_URL_FOR_PERSONS
url_for_bol_suppliers = BOL_BDA_BASE_URL/api/v1/boldata
url_for_projects = BOL_BDA_BASE_URL/api/v1/boldata
url_for_reports = BOL_BDA_BASE_URL/api/v1/boldata
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
scope =
    uapi_contracts_id_delete,
    uapi_contracts_id_document_get,
    uapi_contracts_id_document_put,
    uapi_contracts_id_get,
    uapi_contracts_id_put,
    uapi_contracts_post,
    uapi_contracts_search_id_get,

    uapi_orgs_get,
    uapi_orgs_id_delete,
    uapi_orgs_id_get,
    uapi_orgs_id_put,
    uapi_orgs_post,
    uapi_orgs_search_id_get,
    uapi_orgs_id_sync_get,

    uapi_persons_get,
    uapi_persons_id_delete,
    uapi_persons_id_get,
    uapi_persons_id_private_get,
    uapi_persons_id_private_put,
    uapi_persons_id_put,
    uapi_persons_post,
    uapi_persons_search_id_get,

    uapi_projects_get,
    uapi_projects_id_delete,
    uapi_projects_id_get,
    uapi_projects_id_put,
    uapi_projects_post,
    uapi_projects_search_id_get,
    uapi_projects_id_get,
    uapi_projects_id_put,
    uapi_projects_post,
    uapi_projects_search_id_get,

    uapi_reports_get,
    uapi_reports_id_delete,
    uapi_reports_id_get,
    uapi_reports_id_pdf_get,
    uapi_reports_id_pdf_put,
    uapi_reports_id_put,
    uapi_reports_post,
    uapi_reports_search_id_get,

    uapi_bol_suppliers_get,
    uapi_bol_suppliers_id_delete,
    uapi_bol_suppliers_id_get,
    uapi_bol_suppliers_id_put,
    uapi_bol_suppliers_post,
    uapi_bol_suppliers_search_id_get,

    uapi_jobs_get,
    uapi_jobs_id_delete,
    uapi_jobs_id_get,
    uapi_jobs_id_put,
    uapi_jobs_post,
    uapi_jobs_search_id_get,

threads = BOL_QVARN_THREADPOOL_SIZE
extended_project_fields = yes

[core-system-api]
base_url = BOL_CORESYSAPI_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    view_extended:organisation_person,
    view:person,
    view:organisation,
    subscribe:organisationrecord,
    view:organisationrecord,
    view_report:organisationrecord,

[contract]
base_url = CONTRACT_API_BASE_URL
verify_requests = true
scopes =
    contract_creditsafe_account_read,
    contract_creditsafe_account_create,
    contract_creditsafe_account_delete,
    contract_creditsafe_account_update,

[user-account-api]
base_url = USER_ACCOUNT_API_BASE_URL
client_id = BOL_CLIENT_ID
client_secret = BOL_CLIENT_SECRET
verify_requests = true
threads = 1
scope =
    user_account_read
    user_account_create
    user_account_delete
    user_account_update

[sendgrid]
sendgrid_sender = SENDGRID_SENDER
sendgrid_api_key = SENDGRID_API_KEY

[feature-flags]
company_related_projects = BOL_FEATURE_FLAG_COMPANY_RELATED_PROJECTS
import_sole_traders = BOL_FEATURE_FLAG_IMPORT_SOLE_TRADERS
pagination = BOL_FEATURE_FLAG_PAGINATION
projects = BOL_FEATURE_FLAG_PROJECTS
notifications = BOL_FEATURE_FLAG_NOTIFICATIONS
suppliers = BOL_FEATURE_FLAG_SUPPLIERS
visitors = BOL_FEATURE_FLAG_VISITORS
project_report = BOL_FEATURE_FLAG_PROJECT_REPORT
search = BOL_FEATURE_FLAG_SEARCH
archived_reports = BOL_FEATURE_FLAG_ARCHIVED_REPORTS
extended_report = BOL_FEATURE_FLAG_EXTENDED_REPORT
allow_sp_admin_access = BOL_FEATURE_FLAG_ALLOW_SP_ADMIN_ACCESS
use_stv_theme = BOL_FEATURE_FLAG_USE_STV_THEME
require_creditsafe_contract = BOL_FEATURE_FLAG_REQUIRE_CREDITSAFE_CONTRACT
finnchat = BOL_FEATURE_FLAG_FINNCHAT
disable_emails = BOL_FEATURE_FLAG_DISABLE_EMAILS
company_registry = BOL_FEATURE_FLAG_COMPANY_REGISTRY
celery_for_sendgrid = BOL_FEATURE_FLAG_CELERY_FOR_SENDGRID
bda_client = BOL_FEATURE_FLAG_BDA_CLIENT
bda_company_list = BOL_FEATURE_FLAG_BDA_COMPANY_LIST
bda_project_suppliers = BOL_FEATURE_FLAG_BDA_PROJECT_SUPPLIERS
user_account_api = BOL_FEATURE_FLAG_USER_ACCOUNT_API
web_reports = BOL_FEATURE_FLAG_WEB_REPORTS
web_reports_ee = BOL_FEATURE_FLAG_WEB_REPORTS_EE
web_reports_combined = BOL_FEATURE_FLAG_WEB_REPORTS_COMBINED
cqpoc_client = BOL_FEATURE_FLAG_CQPOC_CLIENT
cqpoc_get_companies = BOL_FEATURE_FLAG_CQPOC_GET_COMPANIES
stamp_workaround_one_site_at_a_time = BOL_FEATURE_FLAG_STAMP_WORKAROUND_ONE_SITE_AT_A_TIME
pre_announcements = BOL_FEATURE_FLAG_PRE_ANNOUNCEMENTS
show_rala_extract = BOL_FEATURE_FLAG_SHOW_RALA_EXTRACT
non_paed_suppliers = BOL_FEATURE_FLAG_NON_PAED_SUPPLIERS
contract_api_creditsafe_contract = BOL_FEATURE_FLAG_CONTRACT_API_CREDITSAFE_CONTRACT
on_azure = BOL_FEATURE_FLAG_ON_AZURE
report_employer_contributions = BOL_FEATURE_FLAG_REPORT_EMPLOYER_CONTRIBUTIONS
lazy_qvarn_startup = BOL_FEATURE_FLAG_LAZY_QVARN_STARTUP
pa_form_checkbox_disabled = BOL_FEATURE_FLAG_PA_FORM_CHECKBOX_DISABLED
add_project_client = BOL_FEATURE_FLAG_ADD_PROJECT_CLIENT
block_project_client = BOL_FEATURE_FLAG_BLOCK_PROJECT_CLIENT
skip_pa_reg_step = BOL_FEATURE_FLAG_SKIP_PA_REG_STEP
create_and_activate_cs_accounts = BOL_FEATURE_FLAG_CREATE_AND_ACTIVATE_CS_ACCOUNTS
person_id_for_project_users = BOL_FEATURE_FLAG_PERSON_ID_FOR_PROJECT_USERS
core_mitt_id06 = BOL_FEATURE_FLAG_CORE_MITT_ID06
project_supplier_comments = BOL_FEATURE_FLAG_PROJECT_SUPPLIER_COMMENTS
skip_gluu_login = BOL_FEATURE_FLAG_SKIP_GLUU_LOGIN

# Logging configuration
# https://docs.python.org/3/library/logging.config.html#logging-config-fileformat

[formatters]
keys = default

[formatter_default]
class = bolfak.logging.JsonFormatter
format =
    [
        "asctime", "levelname", {
            "message": "message",
            "thread": "threadName",
            "path": "pathname",
            "line": "lineno",
            "func": "funcName",
            "request": "requestId",
            "user": "user"
        }
    ]


[handlers]
keys = console

[handler_console]
class = StreamHandler
args = (sys.stdout,)
formatter = default
level = BOL_LOG_LEVEL


[loggers]
keys = root

[logger_root]
level = BOL_LOG_LEVEL
handlers = console
