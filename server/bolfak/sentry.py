import logging

import raven.conf
import raven.contrib.bottle
from raven.handlers.logging import SentryHandler

import bolfak


logger = logging.getLogger(__name__)


class BolfakSentryHandler(SentryHandler):
    """Handler for logging configuration from file.

    Example:

        [handler_sentry]
        class = bolfak.sentry.BolfakSentryHandler
        level = ERROR
        dsn = ('sentry_dsn', {'service': 'myservice'})

    If you configure logging via configuration file, you don't need to call setup_sentry, because it
    would be a duplication.

    """

    def __init__(self, dsn, tags=None, **kwargs):
        super().__init__(
            dsn=dsn,
            tags=tags or {},
            release=bolfak.__version__,
            list_max_length=200,
            include_paths=['bolfak'],
            **kwargs
        )


def setup_sentry(config, tags={}):
    dsn = config.get('main', 'sentry_dsn', fallback=None)
    if dsn:
        logger.info('Reporting errors to <PERSON><PERSON> at %s', dsn)
        handler = BolfakSentryHandler(dsn, tags, level=logging.ERROR)
        raven.conf.setup_logging(handler)
        return handler.client
